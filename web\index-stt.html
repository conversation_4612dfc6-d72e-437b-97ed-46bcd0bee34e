<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Diagnosis Skills Assessment - Speech to Text</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        textarea,
        input,
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        textarea:focus,
        input:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-small {
            padding: 8px 15px;
            font-size: 12px;
        }

        /* Speech-to-Text specific styles */
        .stt-controls {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .stt-controls h4 {
            color: #1e40af;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .stt-button-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 15px;
        }

        .btn-record {
            background: linear-gradient(135deg, #dc2626, #ef4444);
        }

        .btn-record.recording {
            background: linear-gradient(135deg, #059669, #10b981);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.7;
            }

            100% {
                opacity: 1;
            }
        }

        .stt-status {
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .stt-status.idle {
            background: #e5e7eb;
            color: #374151;
        }

        .stt-status.listening {
            background: #dbeafe;
            color: #1e40af;
        }

        .stt-status.processing {
            background: #fef3c7;
            color: #92400e;
        }

        .stt-status.error {
            background: #fee2e2;
            color: #dc2626;
        }

        .stt-transcript {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 12px;
            min-height: 60px;
            font-style: italic;
            color: #64748b;
            margin-bottom: 15px;
        }

        .stt-transcript.has-content {
            color: #1e293b;
            font-style: normal;
        }

        .status-bar {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: 600;
        }

        .status-item .value {
            color: #667eea;
        }

        .scoring-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .scoring-status {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .scoring-status.idle {
            background: #e9ecef;
            color: #666;
        }

        .scoring-status.scoring {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .scoring-status.complete {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .scoring-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .questions-container {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }

        .questions-container::-webkit-scrollbar {
            width: 8px;
        }

        .questions-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .questions-container::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }

        .questions-container::-webkit-scrollbar-thumb:hover {
            background: #5a6fd8;
        }

        .question-item {
            background: white;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .question-id {
            background: #667eea;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .score-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .score-excellent {
            background: #2ecc71;
            color: white;
        }

        .score-good {
            background: #f39c12;
            color: white;
        }

        .score-fair {
            background: #e67e22;
            color: white;
        }

        .score-poor {
            background: #e74c3c;
            color: white;
        }

        .score-pending {
            background: #95a5a6;
            color: white;
        }

        .question-text {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .answer-text {
            color: #666;
            font-style: italic;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .audio-player {
            display: none;
        }

        .play-btn {
            background: linear-gradient(135deg, #16a085, #1abc9c);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .play-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(22, 160, 133, 0.3);
        }

        .play-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .tts-controls {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .tts-controls h4 {
            color: #155724;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .tts-form {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 15px;
            align-items: end;
        }

        .tts-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .tts-checkbox input[type="checkbox"] {
            width: auto;
        }

        .score-summary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .score-summary h3 {
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .score-value {
            font-size: 2.5em;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
        }

        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .score-breakdown-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .score-breakdown-item .number {
            font-size: 1.5em;
            font-weight: bold;
        }

        .score-breakdown-item .label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .scoring-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .scoring-info h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }

        .scoring-info p {
            color: #34495e;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .diagnosis-section {
            background: #f0f9ff;
            border: 2px solid #3b82f6;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
        }

        .diagnosis-section h3 {
            color: #1e40af;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .diagnosis-form {
            display: grid;
            gap: 20px;
        }

        .diagnosis-result {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-radius: 10px;
            padding: 25px;
            margin-top: 20px;
        }

        .diagnosis-score-big {
            font-size: 3em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }

        .feedback-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .feedback-list {
            list-style: none;
            padding: 0;
        }

        .feedback-list li {
            background: rgba(255, 255, 255, 0.1);
            margin: 8px 0;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid rgba(255, 255, 255, 0.3);
        }

        .case-closed {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 20px;
        }

        .case-closed h3 {
            color: #155724;
            margin-bottom: 10px;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .scoring-controls {
                flex-direction: column;
            }

            .tts-form {
                grid-template-columns: 1fr;
            }

            .stt-button-group {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏥 Medical Diagnosis Skills Assessment</h1>
            <p>Practice your diagnostic reasoning with AI-powered patient simulations - Now with Speech-to-Text!</p>
        </div>

        <div class="main-content">
            <!-- Setup Section -->
            <div class="section">
                <h2>📋 Setup Patient Context</h2>
                <div class="form-group">
                    <label for="contextInput">Patient/Case Information:</label>
                    <textarea id="contextInput"
                        placeholder="Enter patient details, symptoms, medical history, etc..."></textarea>
                </div>
                <button class="btn" onclick="setContext()">Set Context</button>
            </div>

            <!-- Stats Section -->
            <div class="section">
                <h2>📊 Session Statistics</h2>
                <div class="status-bar" id="statusBar">
                    <div class="status-item">Questions: <span class="value" id="totalQuestions">0</span></div>
                    <div class="status-item">Scored: <span class="value" id="scoredQuestions">0</span></div>
                    <div class="status-item">Pending: <span class="value" id="pendingQuestions">0</span></div>
                    <div class="status-item">Average: <span class="value" id="averageScore">0</span></div>
                </div>
                <div class="scoring-controls">
                    <button class="btn btn-success" id="scoreAllBtn" onclick="scoreAllQuestions()">📊 Score All
                        Questions</button>
                    <button class="btn btn-secondary" onclick="refreshStats()">🔄 Refresh</button>
                    <button class="btn btn-danger" onclick="clearSession()">🗑️ Clear Session</button>
                </div>
                <div id="scoringStatus" class="scoring-status idle">
                    Ready to score questions
                </div>
            </div>

            <!-- Question Section -->
            <div class="section">
                <h2>❓ Ask Diagnostic Questions</h2>

                <!-- Speech-to-Text Controls -->
                <div class="stt-controls">
                    <h4>🎤 Speech-to-Text</h4>
                    <div class="stt-status idle" id="sttStatus">
                        Click "Start Recording" to speak your question
                    </div>
                    <div class="stt-button-group">
                        <button class="btn btn-record" id="recordBtn" onclick="toggleRecording()">
                            🎤 Start Recording
                        </button>
                        <button class="btn btn-secondary" id="clearTranscriptBtn" onclick="clearTranscript()">
                            🗑️ Clear
                        </button>
                    </div>
                    <div class="stt-transcript" id="transcriptDisplay">
                        Your spoken question will appear here...
                    </div>
                </div>

                <!-- TTS Controls -->
                <div class="tts-controls">
                    <h4>🔊 Voice Settings</h4>
                    <div class="tts-checkbox">
                        <input type="checkbox" id="enableTTS" checked>
                        <label for="enableTTS">Enable patient voice responses</label>
                    </div>
                    <div class="tts-form">
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="voiceGender">Voice Gender:</label>
                            <select id="voiceGender">
                                <option value="FEMALE">Female</option>
                                <option value="MALE">Male</option>
                                <option value="NEUTRAL">Neutral</option>
                            </select>
                        </div>
                        <div class="form-group" style="margin-bottom: 0;">
                            <label for="speakingRate">Speaking Rate:</label>
                            <select id="speakingRate">
                                <option value="0.75">Slow (0.75x)</option>
                                <option value="1.0" selected>Normal (1.0x)</option>
                                <option value="1.25">Fast (1.25x)</option>
                                <option value="1.5">Very Fast (1.5x)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="questionInput">Your Question:</label>
                    <input type="text" id="questionInput" placeholder="What symptoms are you experiencing?"
                        onkeypress="handleQuestionKeyPress(event)">
                </div>
                <button class="btn" id="askButton" onclick="askQuestion()">Ask Question</button>
            </div>

            <!-- Scoring Info -->
            <div class="section">
                <h2>📈 Scoring Information</h2>
                <div class="scoring-info">
                    <h4>How Questions Are Scored:</h4>
                    <p>Questions are evaluated on a 0-10 scale based on:</p>
                    <ul style="margin: 10px 0 10px 20px;">
                        <li>Medical relevance (0-3 points)</li>
                        <li>Clinical reasoning (0-3 points)</li>
                        <li>Clarity & specificity (0-2 points)</li>
                        <li>Diagnostic value (0-1 point)</li>
                        <li>Professional phrasing (0-1 point)</li>
                    </ul>
                    <p><strong>Note:</strong> Questions are now scored in batches for improved efficiency. Click "Score
                        All Questions" when you're ready to evaluate your performance.</p>
                </div>
            </div>

            <!-- Current Scores Section -->
            <div class="section full-width">
                <h2>📈 Current Scores</h2>
                <div id="scoresSummary">
                    <p>No questions asked yet. Start by setting a patient context and asking diagnostic questions.</p>
                </div>
            </div>

            <!-- Diagnosis Submission Section -->
            <div class="section full-width">
                <h2>🎯 Submit Final Diagnosis</h2>
                <div id="diagnosisSection">
                    <div class="diagnosis-section">
                        <h3>Ready to Submit Your Diagnosis?</h3>
                        <p style="margin-bottom: 20px; color: #64748b;">Once you've gathered enough information through
                            your questions, submit your final diagnosis and reasoning to complete the case assessment.
                        </p>

                        <div class="diagnosis-form">
                            <div class="form-group">
                                <label for="diagnosisInput">Your Diagnosis:</label>
                                <input type="text" id="diagnosisInput"
                                    placeholder="Enter your primary diagnosis (e.g., Acute Myocardial Infarction)" />
                            </div>

                            <div class="form-group">
                                <label for="reasoningInput">Diagnostic Reasoning:</label>
                                <textarea id="reasoningInput"
                                    placeholder="Explain your diagnostic reasoning, including key symptoms, risk factors, and clinical reasoning that led to this diagnosis..."
                                    style="min-height: 120px;"></textarea>
                            </div>

                            <button class="btn btn-warning" id="submitDiagnosisBtn" onclick="submitDiagnosis()">
                                🎯 Submit Diagnosis & Close Case
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Diagnosis Results (hidden initially) -->
                <div id="diagnosisResults" class="hidden">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Questions History -->
            <div class="section full-width">
                <h2>💬 Questions & Responses</h2>
                <div id="questionsHistory" class="questions-container">
                    <p>Your questions and the patient's responses will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001'; // Different port for STT version
        let pollInterval;
        let scoringInProgress = false;
        let caseSubmitted = false;
        let currentAudio = null;

        // Speech-to-Text variables
        let recognition = null;
        let isRecording = false;
        let finalTranscript = '';

        window.onload = function () {
            testApiConnection();
            refreshStats();
            startPolling();
            initializeSpeechRecognition();
        };

        function initializeSpeechRecognition() {
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();

                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'en-US';

                recognition.onstart = function () {
                    updateSTTStatus('listening', '🎤 Listening... Speak your question now');
                };

                recognition.onresult = function (event) {
                    let interimTranscript = '';
                    finalTranscript = '';

                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        const transcript = event.results[i][0].transcript;
                        if (event.results[i].isFinal) {
                            finalTranscript += transcript;
                        } else {
                            interimTranscript += transcript;
                        }
                    }

                    const transcriptDisplay = document.getElementById('transcriptDisplay');
                    transcriptDisplay.textContent = finalTranscript + interimTranscript;
                    transcriptDisplay.classList.add('has-content');

                    // Update the question input field
                    if (finalTranscript) {
                        document.getElementById('questionInput').value = finalTranscript.trim();
                    }
                };

                recognition.onerror = function (event) {
                    console.error('Speech recognition error:', event.error);
                    updateSTTStatus('error', `❌ Error: ${event.error}`);
                    stopRecording();
                };

                recognition.onend = function () {
                    if (isRecording) {
                        updateSTTStatus('idle', 'Recording stopped. Click "Start Recording" to record again');
                        stopRecording();
                    }
                };
            } else {
                updateSTTStatus('error', '❌ Speech recognition not supported in this browser');
                document.getElementById('recordBtn').disabled = true;
            }
        }

        function toggleRecording() {
            if (!recognition) {
                alert('Speech recognition is not available in this browser.');
                return;
            }

            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        function startRecording() {
            try {
                isRecording = true;
                finalTranscript = '';

                const recordBtn = document.getElementById('recordBtn');
                recordBtn.textContent = '⏹️ Stop Recording';
                recordBtn.classList.add('recording');

                updateSTTStatus('listening', '🎤 Initializing microphone...');
                recognition.start();
            } catch (error) {
                console.error('Error starting recording:', error);
                updateSTTStatus('error', '❌ Failed to start recording');
                stopRecording();
            }
        }

        function stopRecording() {
            if (recognition && isRecording) {
                recognition.stop();
            }

            isRecording = false;
            const recordBtn = document.getElementById('recordBtn');
            recordBtn.textContent = '🎤 Start Recording';
            recordBtn.classList.remove('recording');

            if (finalTranscript) {
                updateSTTStatus('idle', '✅ Recording complete. You can edit the text or ask the question.');
            } else {
                updateSTTStatus('idle', 'Click "Start Recording" to speak your question');
            }
        }

        function updateSTTStatus(status, message) {
            const statusElement = document.getElementById('sttStatus');
            statusElement.className = `stt-status ${status}`;
            statusElement.textContent = message;
        }

        function clearTranscript() {
            finalTranscript = '';
            const transcriptDisplay = document.getElementById('transcriptDisplay');
            transcriptDisplay.textContent = 'Your spoken question will appear here...';
            transcriptDisplay.classList.remove('has-content');
            document.getElementById('questionInput').value = '';
            updateSTTStatus('idle', 'Transcript cleared. Click "Start Recording" to speak your question');
        }

        async function testApiConnection() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const result = await response.json();
                console.log('API Connection successful:', result);
            } catch (error) {
                alert(`Cannot connect to API server at ${API_BASE}. Error: ${error.message}`);
            }
        }

        async function setContext() {
            const context = document.getElementById('contextInput').value.trim();
            if (!context) { alert('Please enter patient context first.'); return; }

            try {
                const response = await fetch(`${API_BASE}/set-context`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ context })
                });
                if (!response.ok) throw new Error('Error setting context');
                alert('Context set successfully!');
                refreshStats();
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function askQuestion() {
            const question = document.getElementById('questionInput').value.trim();
            if (!question) { alert('Please enter a question first.'); return; }

            const askButton = document.getElementById('askButton');
            askButton.disabled = true;
            askButton.innerHTML = '<span class="loading"></span> Asking...';

            // Get TTS settings
            const includeTTS = document.getElementById('enableTTS').checked;
            const voiceGender = document.getElementById('voiceGender').value;
            const speakingRate = parseFloat(document.getElementById('speakingRate').value);

            try {
                const response = await fetch(`${API_BASE}/ask-question`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        question,
                        include_audio: includeTTS,
                        voice_gender: voiceGender,
                        speaking_rate: speakingRate
                    })
                });
                const result = await response.json();
                if (response.ok) {
                    document.getElementById('questionInput').value = '';

                    // Clear the speech transcript as well
                    clearTranscript();

                    // If audio was generated, play it automatically
                    if (result.audio_base64 && includeTTS) {
                        playAudioFromBase64(result.audio_base64);
                    }

                    loadQuestions();
                    refreshStats();
                    updateScoringButtonState();
                } else {
                    alert('Error asking question: ' + result.detail);
                }
            } catch (error) {
                alert('Error connecting to server: ' + error.message);
            } finally {
                askButton.disabled = false;
                askButton.innerHTML = 'Ask Question';
            }
        }

        function handleQuestionKeyPress(event) {
            if (event.key === 'Enter') { askQuestion(); }
        }

        async function playQuestionAudio(questionId) {
            const playBtn = document.getElementById(`playBtn_${questionId}`);
            const voiceGender = document.getElementById('voiceGender').value;
            const speakingRate = parseFloat(document.getElementById('speakingRate').value);

            try {
                playBtn.disabled = true;
                playBtn.innerHTML = '<span class="loading"></span>';

                const response = await fetch(`${API_BASE}/tts-audio/${questionId}?voice_gender=${voiceGender}&speaking_rate=${speakingRate}`);

                if (response.ok) {
                    const audioBlob = await response.blob();
                    const audioUrl = URL.createObjectURL(audioBlob);

                    // Stop any currently playing audio
                    if (currentAudio) {
                        currentAudio.pause();
                        currentAudio = null;
                    }

                    // Create and play new audio
                    currentAudio = new Audio(audioUrl);
                    currentAudio.play();

                    // Reset button when audio ends
                    currentAudio.onended = () => {
                        playBtn.innerHTML = '🔊 Play';
                        playBtn.disabled = false;
                        URL.revokeObjectURL(audioUrl);
                        currentAudio = null;
                    };

                    // Reset button if there's an error
                    currentAudio.onerror = () => {
                        playBtn.innerHTML = '❌ Error';
                        playBtn.disabled = false;
                        URL.revokeObjectURL(audioUrl);
                        currentAudio = null;
                    };

                    playBtn.innerHTML = '⏸️ Playing';

                } else {
                    throw new Error('Failed to generate audio');
                }

            } catch (error) {
                console.error('Error playing audio:', error);
                playBtn.innerHTML = '❌ Error';
                setTimeout(() => {
                    playBtn.innerHTML = '🔊 Play';
                    playBtn.disabled = false;
                }, 2000);
            }
        }

        function playAudioFromBase64(audioBase64) {
            try {
                // Stop any currently playing audio
                if (currentAudio) {
                    currentAudio.pause();
                    currentAudio = null;
                }

                // Convert base64 to blob
                const byteCharacters = atob(audioBase64);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const audioBlob = new Blob([byteArray], { type: 'audio/mpeg' });
                const audioUrl = URL.createObjectURL(audioBlob);

                // Create and play audio
                currentAudio = new Audio(audioUrl);
                currentAudio.play();

                // Clean up when done
                currentAudio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                    currentAudio = null;
                };

            } catch (error) {
                console.error('Error playing audio from base64:', error);
            }
        }

        async function scoreAllQuestions() {
            if (scoringInProgress) {
                alert('Scoring is already in progress. Please wait...');
                return;
            }

            const scoreBtn = document.getElementById('scoreAllBtn');
            const statusDiv = document.getElementById('scoringStatus');

            try {
                scoringInProgress = true;
                scoreBtn.disabled = true;
                scoreBtn.innerHTML = '<span class="loading"></span> Scoring...';

                statusDiv.className = 'scoring-status scoring';
                statusDiv.innerHTML = '⏳ Scoring all questions in batch...';

                const response = await fetch(`${API_BASE}/score-all`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (response.ok) {
                    statusDiv.innerHTML = `✅ Scoring initiated for ${result.unscored_questions} questions. Results will update automatically.`;
                    // Start polling more frequently during scoring
                    clearInterval(pollInterval);
                    pollInterval = setInterval(refreshStats, 2000);

                    // Reset to normal polling after 30 seconds
                    setTimeout(() => {
                        clearInterval(pollInterval);
                        startPolling();
                    }, 30000);

                } else {
                    throw new Error(result.detail || 'Failed to initiate scoring');
                }
            } catch (error) {
                statusDiv.className = 'scoring-status error';
                statusDiv.innerHTML = `❌ Error: ${error.message}`;
                console.error('Scoring error:', error);
            } finally {
                scoringInProgress = false;
                scoreBtn.disabled = false;
                scoreBtn.innerHTML = '📊 Score All Questions';
            }
        }

        async function submitDiagnosis() {
            if (caseSubmitted) {
                alert('Case has already been submitted!');
                return;
            }

            const diagnosis = document.getElementById('diagnosisInput').value.trim();
            const reasoning = document.getElementById('reasoningInput').value.trim();

            if (!diagnosis) {
                alert('Please enter your diagnosis.');
                return;
            }

            if (!reasoning) {
                alert('Please provide your diagnostic reasoning.');
                return;
            }

            const submitBtn = document.getElementById('submitDiagnosisBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> Evaluating Diagnosis...';

            try {
                const response = await fetch(`${API_BASE}/score-diagnosis`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        diagnosis: diagnosis,
                        reasoning: reasoning
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    displayDiagnosisResults(result, diagnosis, reasoning);
                    caseSubmitted = true;
                    disableCaseInteractions();
                } else {
                    const error = await response.json();
                    alert('Error evaluating diagnosis: ' + (error.detail || 'Unknown error'));
                }
            } catch (error) {
                alert('Error connecting to server: ' + error.message);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '🎯 Submit Diagnosis & Close Case';
            }
        }

        function displayDiagnosisResults(result, diagnosis, reasoning) {
            const resultsDiv = document.getElementById('diagnosisResults');
            const diagnosisDiv = document.getElementById('diagnosisSection');

            // Hide the submission form
            diagnosisDiv.style.display = 'none';

            // Show results
            resultsDiv.className = '';
            resultsDiv.innerHTML = `
                <div class="diagnosis-result">
                    <h3>🎯 Diagnosis Evaluation Complete</h3>
                    <div class="diagnosis-score-big">${result.diagnosis_score}/100</div>

                    <div class="case-closed">
                        <h3>📋 Case Summary</h3>
                        <p><strong>Your Diagnosis:</strong> ${diagnosis}</p>
                        <p><strong>Final Score:</strong> ${result.diagnosis_score}/100</p>
                    </div>

                    <div class="feedback-section">
                        <h4>📝 Detailed Feedback</h4>
                        <p style="line-height: 1.6; margin-bottom: 20px;">${result.feedback}</p>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                            <div>
                                <h4>💪 Strengths Demonstrated</h4>
                                <ul class="feedback-list">
                                    ${result.strengths.map(strength => `<li>✓ ${strength}</li>`).join('')}
                                </ul>
                            </div>

                            <div>
                                <h4>📈 Areas for Improvement</h4>
                                <ul class="feedback-list">
                                    ${result.areas_for_improvement.map(area => `<li>→ ${area}</li>`).join('')}
                                </ul>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            <h4>🤔 Your Reasoning</h4>
                            <p style="font-style: italic; line-height: 1.6;">"${reasoning}"</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function disableCaseInteractions() {
            // Stop any playing audio
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }

            // Stop any ongoing speech recognition
            if (isRecording) {
                stopRecording();
            }

            // Disable question asking
            document.getElementById('askButton').disabled = true;
            document.getElementById('askButton').innerHTML = '❌ Case Closed';
            document.getElementById('questionInput').disabled = true;

            // Disable context setting
            const contextInput = document.getElementById('contextInput');
            contextInput.disabled = true;

            // Disable TTS controls
            document.getElementById('enableTTS').disabled = true;
            document.getElementById('voiceGender').disabled = true;
            document.getElementById('speakingRate').disabled = true;

            // Disable STT controls
            document.getElementById('recordBtn').disabled = true;
            document.getElementById('clearTranscriptBtn').disabled = true;

            // Update all buttons to show case is closed
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                if (btn.id !== 'submitDiagnosisBtn') {
                    btn.disabled = true;
                }
            });
        }

        function enableCaseInteractions() {
            // Re-enable question asking
            document.getElementById('askButton').disabled = false;
            document.getElementById('askButton').innerHTML = 'Ask Question';
            document.getElementById('questionInput').disabled = false;

            // Re-enable context setting
            document.getElementById('contextInput').disabled = false;

            // Re-enable TTS controls
            document.getElementById('enableTTS').disabled = false;
            document.getElementById('voiceGender').disabled = false;
            document.getElementById('speakingRate').disabled = false;

            // Re-enable STT controls
            document.getElementById('recordBtn').disabled = false;
            document.getElementById('clearTranscriptBtn').disabled = false;

            // Re-enable all buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.disabled = false;
            });

            caseSubmitted = false;
        }

        function updateScoringButtonState() {
            const scoreBtn = document.getElementById('scoreAllBtn');
            const totalQuestions = parseInt(document.getElementById('totalQuestions').textContent);
            const pendingQuestions = parseInt(document.getElementById('pendingQuestions').textContent);

            if (totalQuestions === 0) {
                scoreBtn.disabled = true;
                scoreBtn.innerHTML = '📊 No Questions to Score';
            } else if (pendingQuestions === 0) {
                scoreBtn.disabled = true;
                scoreBtn.innerHTML = '✅ All Questions Scored';
            } else {
                scoreBtn.disabled = false;
                scoreBtn.innerHTML = `📊 Score ${pendingQuestions} Questions`;
            }
        }

        async function loadQuestions() {
            try {
                const response = await fetch(`${API_BASE}/questions`);
                const result = await response.json();
                const historyDiv = document.getElementById('questionsHistory');
                if (result.questions.length === 0) {
                    historyDiv.innerHTML = '<p>Your questions and the patient\'s responses will appear here...</p>';
                    return;
                }
                historyDiv.innerHTML = result.questions.map(q => {
                    const scoreClass = getScoreClass(q.score);
                    const scoreText = q.score !== null ? `${q.score}/10` : 'Pending';
                    const scoringType = q.scoring_type ? ` (${q.scoring_type})` : '';
                    const audioButton = `
                        <div class="audio-controls">
                            <button class="play-btn btn-small" id="playBtn_${q.id}" onclick="playQuestionAudio(${q.id})">
                                🔊 Play
                            </button>
                        </div>
                    `;

                    return `
                        <div class="question-item">
                            <div class="question-header">
                                <span class="question-id">#${q.id}</span>
                                <span class="score-badge ${scoreClass}">${scoreText}${scoringType}</span>
                            </div>
                            <div class="question-text">Q: ${q.question}</div>
                            <div class="answer-text">A: ${q.answer}</div>
                            ${audioButton}
                        </div>
                    `;
                }).join('');
            } catch (error) {
                console.error('Error loading questions:', error);
            }
        }

        function getScoreClass(score) {
            if (score === null) return 'score-pending';
            if (score >= 8) return 'score-excellent';
            if (score >= 6) return 'score-good';
            if (score >= 4) return 'score-fair';
            return 'score-poor';
        }

        async function getScores() {
            try {
                const response = await fetch(`${API_BASE}/score`);
                const result = await response.json();
                const summaryDiv = document.getElementById('scoresSummary');
                if (result.total_questions === 0) {
                    summaryDiv.innerHTML = '<p>No questions asked yet. Start by setting a patient context and asking diagnostic questions.</p>';
                    return;
                }

                // Count score distribution
                const scoreDistribution = { excellent: 0, good: 0, fair: 0, poor: 0, pending: 0 };
                result.individual_scores.forEach(q => {
                    if (q.score === null) scoreDistribution.pending++;
                    else if (q.score >= 8) scoreDistribution.excellent++;
                    else if (q.score >= 6) scoreDistribution.good++;
                    else if (q.score >= 4) scoreDistribution.fair++;
                    else scoreDistribution.poor++;
                });

                summaryDiv.innerHTML = `
                    <div class="score-summary">
                        <h3>Overall Performance</h3>
                        <div class="score-value">${result.average_score}/10</div>
                        <p><strong>Questions:</strong> ${result.scored_questions}/${result.total_questions} scored</p>
                        ${result.pending_questions > 0 ? `<p><strong>Pending:</strong> ${result.pending_questions} questions awaiting scores</p>` : ''}

                        ${result.scored_questions > 0 ? `
                        <div class="score-breakdown">
                            <div class="score-breakdown-item">
                                <div class="number">${scoreDistribution.excellent}</div>
                                <div class="label">Excellent (8-10)</div>
                            </div>
                            <div class="score-breakdown-item">
                                <div class="number">${scoreDistribution.good}</div>
                                <div class="label">Good (6-7)</div>
                            </div>
                            <div class="score-breakdown-item">
                                <div class="number">${scoreDistribution.fair}</div>
                                <div class="label">Fair (4-5)</div>
                            </div>
                            <div class="score-breakdown-item">
                                <div class="number">${scoreDistribution.poor}</div>
                                <div class="label">Poor (0-3)</div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                `;
            } catch (error) {
                console.error('Error getting scores:', error);
                document.getElementById('scoresSummary').innerHTML = '<p>Error loading scores.</p>';
            }
        }

        async function refreshStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const result = await response.json();

                document.getElementById('totalQuestions').textContent = result.total_questions;
                document.getElementById('scoredQuestions').textContent = result.scored_questions;
                document.getElementById('pendingQuestions').textContent = result.pending_questions;
                document.getElementById('averageScore').textContent = result.average_score;

                // Update scoring status
                const statusDiv = document.getElementById('scoringStatus');
                if (!scoringInProgress) {
                    if (result.pending_questions > 0 && result.scored_questions > 0) {
                        statusDiv.className = 'scoring-status scoring';
                        statusDiv.innerHTML = `⏳ ${result.pending_questions} questions being scored...`;
                    } else if (result.pending_questions === 0 && result.total_questions > 0) {
                        statusDiv.className = 'scoring-status complete';
                        statusDiv.innerHTML = '✅ All questions have been scored';
                    } else {
                        statusDiv.className = 'scoring-status idle';
                        statusDiv.innerHTML = result.total_questions === 0 ? 'Ready to score questions' : `Ready to score ${result.pending_questions} questions`;
                    }
                }

                updateScoringButtonState();
                loadQuestions();
                if (result.total_questions > 0) { getScores(); }
            } catch (error) {
                console.error('Error refreshing stats:', error);
            }
        }

        async function clearSession() {
            if (!confirm('Are you sure you want to clear all questions and start over?')) return;
            try {
                const response = await fetch(`${API_BASE}/questions`, { method: 'DELETE' });
                if (response.ok) {
                    // Reset all UI elements
                    refreshStats();
                    document.getElementById('questionsHistory').innerHTML = '<p>Your questions and the patient\'s responses will appear here...</p>';
                    document.getElementById('scoresSummary').innerHTML = '<p>No questions asked yet. Start by setting a patient context and asking diagnostic questions.</p>';
                    document.getElementById('contextInput').value = '';
                    document.getElementById('diagnosisInput').value = '';
                    document.getElementById('reasoningInput').value = '';

                    // Reset diagnosis section
                    document.getElementById('diagnosisSection').style.display = 'block';
                    document.getElementById('diagnosisResults').className = 'hidden';

                    const statusDiv = document.getElementById('scoringStatus');
                    statusDiv.className = 'scoring-status idle';
                    statusDiv.innerHTML = 'Ready to score questions';

                    // Clear speech transcript
                    clearTranscript();

                    // Re-enable interactions
                    enableCaseInteractions();

                    alert('Session cleared successfully!');
                } else {
                    alert('Error clearing session.');
                }
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        function startPolling() {
            clearInterval(pollInterval);
            pollInterval = setInterval(refreshStats, 5000);
        }

        document.addEventListener('visibilitychange', function () {
            if (document.hidden) {
                clearInterval(pollInterval);
            } else {
                startPolling();
            }
        });
    </script>
</body>

</html>