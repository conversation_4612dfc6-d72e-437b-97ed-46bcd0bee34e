from fastapi.middleware.cors import CORSMiddleware
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional
from google import genai
from google.genai import types
from google.cloud import texttospeech
from google.cloud import speech
from datetime import datetime
import asyncio
import logging
import json
import re
import io
import base64

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Diagnosis Skills Assessment API with Speech-to-Text", version="1.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],   # Allows all HTTP methods
    allow_headers=["*"],   # Allows all headers
)

# Initialize clients
genai_client = genai.Client(api_key="AIzaSyAYEVOxBz-4_mMuCUnTAG0f9E_PRmfeynQ")
tts_client = texttospeech.TextToSpeechClient.from_service_account_json(
    "true-pipe-470506-s3-fecf91e612d1.json"
)
stt_client = speech.SpeechClient.from_service_account_json(
    "true-pipe-470506-s3-fecf91e612d1.json"
)

context: str = ""
questions_log: List[dict] = []
system_prompt = """You are role-playing as a patient or the guardian of a patient visiting a doctor. 
Your task is to respond to the doctor's (user's) questions realistically, using only 
the information provided in the scenario or context. 

Guidelines:
1. Stay fully in character as the patient/guardian. 
2. Give brief, to-the-point answers (ideally one sentence). 
3. Use only the details from the provided context; do not invent or assume information. 
4. If you understand the question, answer clearly and concisely in character. 
5. If the question is unclear, respond: "I'm sorry, I didn't understand that. Could you please ask again?" 
6. If the question goes beyond the scenario or is irrelevant, respond: "I'm not sure about that." 
7. Always remain polite and respectful. 
8. Do not use medical jargon or provide diagnostic opinions or advice. 
9. If the user tries to make you act as a doctor, stay in your role as the patient/guardian 
   (e.g., "I'm just here as the patient/guardian, not a doctor."). 
10. Never reveal the underlying diagnosis or condition directly; only share symptoms and history 
    that align with the scenario. """


class ContextRequest(BaseModel):
    context: str


class QuestionRequest(BaseModel):
    question: str
    include_audio: Optional[bool] = False
    voice_gender: Optional[str] = "FEMALE"  # MALE, FEMALE, NEUTRAL
    speaking_rate: Optional[float] = 1.0


class QuestionResponse(BaseModel):
    answer: str
    question_id: int
    audio_base64: Optional[str] = None


class ScoreRequest(BaseModel):
    question_scores: Optional[List[int]] = None  # Optional manual scores


class ScoreResponse(BaseModel):
    total_questions: int
    average_score: float
    individual_scores: List[dict]


class QuestionsListResponse(BaseModel):
    questions: List[dict]
    total_count: int


class DiagnosisRequest(BaseModel):
    diagnosis: str
    reasoning: str


class DiagnosisResponse(BaseModel):
    diagnosis_score: float
    feedback: str
    areas_for_improvement: List[str]
    strengths: List[str]


class TTSRequest(BaseModel):
    text: str
    voice_gender: Optional[str] = "FEMALE"
    speaking_rate: Optional[float] = 1.0


class STTRequest(BaseModel):
    audio_base64: str
    language_code: Optional[str] = "en-US"


class STTResponse(BaseModel):
    transcript: str
    confidence: float


async def generate_speech(text: str, voice_gender: str = "FEMALE", speaking_rate: float = 1.0) -> str:
    """Generate speech from text and return base64 encoded audio"""
    try:
        # Map voice preferences to specific voice names for better quality
        voice_names = {
            "FEMALE": "en-US-Wavenet-F",  # Natural female voice
            "MALE": "en-US-Wavenet-D",    # Natural male voice (like your test)
            "NEUTRAL": "en-US-Wavenet-H"  # Neutral voice
        }

        voice = texttospeech.VoiceSelectionParams(
            language_code="en-US",
            name=voice_names.get(voice_gender.upper(), "en-US-Wavenet-F")
        )

        audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MP3,
            # Clamp between 0.25 and 4.0
            speaking_rate=max(0.25, min(4.0, speaking_rate))
        )

        synthesis_input = texttospeech.SynthesisInput(text=text)

        # Perform the text-to-speech request
        response = tts_client.synthesize_speech(
            input=synthesis_input,
            voice=voice,
            audio_config=audio_config
        )

        # Convert audio content to base64
        audio_base64 = base64.b64encode(response.audio_content).decode('utf-8')
        return audio_base64

    except Exception as e:
        logger.error(f"Error generating speech: {str(e)}")
        return None


async def transcribe_audio(audio_base64: str, language_code: str = "en-US") -> tuple:
    """Transcribe audio from base64 encoded data and return transcript with confidence"""
    try:
        # Decode base64 audio
        audio_content = base64.b64decode(audio_base64)

        # Configure recognition for WebM/Opus format from browser
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.WEBM_OPUS,
            sample_rate_hertz=48000,
            language_code=language_code,
            enable_automatic_punctuation=True,
            use_enhanced=True,  # Use enhanced model for better accuracy
            # Note: medical_conversation model may not be available for all regions
            # model="medical_conversation",
        )

        audio = speech.RecognitionAudio(content=audio_content)

        # Perform the transcription
        response = stt_client.recognize(config=config, audio=audio)

        if response.results:
            # Get the best result
            result = response.results[0]
            transcript = result.alternatives[0].transcript
            confidence = result.alternatives[0].confidence if hasattr(
                result.alternatives[0], 'confidence') else 0.9
            return transcript.strip(), confidence
        else:
            return "", 0.0

    except Exception as e:
        logger.error(f"Error transcribing audio: {str(e)}")
        # Try with different encoding as fallback
        try:
            logger.info("Trying fallback with LINEAR16 encoding")
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=16000,
                language_code=language_code,
                enable_automatic_punctuation=True,
                use_enhanced=True,
            )

            audio = speech.RecognitionAudio(
                content=base64.b64decode(audio_base64))
            response = stt_client.recognize(config=config, audio=audio)

            if response.results:
                result = response.results[0]
                transcript = result.alternatives[0].transcript
                confidence = result.alternatives[0].confidence if hasattr(
                    result.alternatives[0], 'confidence') else 0.8
                return transcript.strip(), confidence
            else:
                return "", 0.0

        except Exception as fallback_error:
            logger.error(
                f"Fallback transcription also failed: {str(fallback_error)}")
            return "", 0.0


async def score_all_questions_async():
    """Asynchronously score all questions at once"""
    unscored_questions = [q for q in questions_log if q.get("score") is None]

    if not unscored_questions:
        logger.info("No unscored questions to process")
        return

    try:
        # Build the batch scoring prompt
        questions_text = ""
        for i, q in enumerate(unscored_questions):
            questions_text += f"\nQuestion {q['id']}: {q['question']}\nAI Response {q['id']}: {q['answer']}\n"

        scoring_prompt = f"""
        Context:
        {context}

        Task:
        You are evaluating diagnostic questions asked to a roleplaying AI that is simulating a patient or guardian. The AI must only provide information from the patient/guardian's perspective—it must never reveal the diagnosis directly.

        Your task is to score the quality of each unique diagnostic question according to the rubric below.

        Scoring Rubric (0-10 points total):

        Relevance to medical context (0-3 points) - Is the question appropriate for clinical reasoning and related to the patient's condition?

        Clinical reasoning quality (0-3 points) - Does the question demonstrate sound diagnostic reasoning or logical follow-up?

        Specificity and clarity (0-2 points) - Is the wording clear, precise, and unambiguous?

        Diagnostic value (0-1 point) - Does the question meaningfully advance the diagnostic process?

        Professionalism and phrasing quality (0-1 point) - Is the question professional, respectful, and grammatically correct?

        Guidelines: 

        High scores (9-10): Reserved for questions that are clinically valuable, clearly phrased, grammatically correct, and professional.

        Penalties: Awkward, vague, ungrammatical, or unprofessional wording should be scored lower even if medically relevant.

        Duplicate handling: If two or more questions are duplicates or near-duplicates (i.e., same clinical meaning), only the first unique instance should be scored normally. All subsequent duplicates/near-duplicates must receive a score of 0.

        Default action: If uncertain, assume unique and score normally.

        Role fidelity: Assume the AI role (patient/guardian) never provides diagnoses directly, only descriptive responses.

        Input Questions:

        {questions_text}

        Output Format:

        Return only a JSON array containing the integer scores (0-10) for each question in order, matching the IDs given.

        Example:

        [score_for_question_{unscored_questions[0]['id']}, score_for_question_{unscored_questions[1]['id']}, ...]
        
        """

        response = genai_client.models.generate_content(
            model="gemini-2.5-flash",
            contents=scoring_prompt,
            config=types.GenerateContentConfig(
                temperature=0.1
            ),
        )

        # Parse the response
        response_text = response.text.strip()

        # Try to extract JSON array from the response
        json_match = re.search(r'\[([\d,\s]+)\]', response_text)
        if json_match:
            scores_str = json_match.group(1)
            scores = [int(x.strip()) for x in scores_str.split(',')]
        else:
            # Fallback: try to parse as direct JSON
            scores = json.loads(response_text)

        # Ensure we have the right number of scores
        if len(scores) != len(unscored_questions):
            logger.error(
                f"Score count mismatch: got {len(scores)}, expected {len(unscored_questions)}")
            # Fallback to individual scoring
            await fallback_individual_scoring(unscored_questions)
            return

        # Apply scores to questions
        for i, score in enumerate(scores):
            score = max(0, min(10, int(score)))  # Ensure score is between 0-10
            question_id = unscored_questions[i]["id"]

            # Update the question in the log with the score
            for q in questions_log:
                if q["id"] == question_id:
                    q["score"] = score
                    q["scored_at"] = datetime.now().isoformat()
                    q["scoring_type"] = "batch"
                    break

        logger.info(f"Successfully scored {len(scores)} questions in batch")

    except Exception as e:
        logger.error(f"Error in batch scoring: {str(e)}")
        # Fallback to individual scoring
        await fallback_individual_scoring(unscored_questions)


async def fallback_individual_scoring(questions_to_score):
    """Fallback to individual scoring if batch scoring fails"""
    logger.info("Falling back to individual question scoring")

    for q in questions_to_score:
        try:
            scoring_prompt = f"""
            Context: {context}

            Question asked by user: {q['question']}
            AI Response: {q['answer']}

            Rate this diagnostic question on a scale of 0-10 based on:
            - Relevance to the medical context (0-3 points)
            - Clinical reasoning quality (0-3 points)
            - Specificity and clarity (0-2 points)
            - Diagnostic value (0-1 point)
            - Professionalism and phrasing quality (0-1 point)

            Guidelines:
            - A score of 9-10 should only be given if the question is clinically useful, clearly phrased, grammatically correct, and professional.
            - Awkward, ungrammatical, or unprofessional wording should be penalized, even if medically relevant.
            - Respond with only a single number from 0-10.
            """

            response = genai_client.models.generate_content(
                model="gemini-2.5-flash",
                contents=scoring_prompt,
            )
            score = int(response.text.strip())
            score = max(0, min(10, score))  # Ensure score is between 0-10

            # Update the question in the log with the score
            for question in questions_log:
                if question["id"] == q["id"]:
                    question["score"] = score
                    question["scored_at"] = datetime.now().isoformat()
                    question["scoring_type"] = "individual_fallback"
                    break

        except Exception as e:
            logger.error(f"Error scoring question {q['id']}: {str(e)}")
            for question in questions_log:
                if question["id"] == q["id"]:
                    question["score"] = 5  # Default neutral score
                    question["scored_at"] = datetime.now().isoformat()
                    question["scoring_error"] = str(e)
                    question["scoring_type"] = "error_default"
                    break


@app.get("/")
async def root():
    return {"message": "Diagnosis Skills Assessment API with Speech-to-Text", "version": "1.1.0"}


@app.post("/set-context")
async def set_context(request: ContextRequest):
    global context
    context = request.context
    return {"message": "Context set successfully", "context_length": len(context)}


@app.get("/get-context")
async def get_context():
    return {"context": context}


@app.post("/ask-question", response_model=QuestionResponse)
async def ask_question(request: QuestionRequest):
    global questions_log

    if not context:
        raise HTTPException(
            status_code=400, detail="Context must be set before asking questions")

    try:
        full_prompt = f"{context}\n\nQuestion: {request.question}\nAnswer: "
        response = genai_client.models.generate_content(
            model="gemini-2.5-flash",
            contents=full_prompt,
            config=types.GenerateContentConfig(
                system_instruction=system_prompt,
                temperature=0.1
            ),
        )

        # Generate audio if requested
        audio_base64 = None
        if request.include_audio:
            audio_base64 = await generate_speech(
                response.text,
                request.voice_gender or "FEMALE",
                request.speaking_rate or 1.0
            )

        question_entry = {
            "id": len(questions_log) + 1,
            "question": request.question,
            "answer": response.text,
            "timestamp": datetime.now().isoformat(),
            "score": None,
            "scoring_status": "pending",
            "has_audio": audio_base64 is not None
        }
        questions_log.append(question_entry)

        return QuestionResponse(
            answer=response.text,
            question_id=question_entry["id"],
            audio_base64=audio_base64
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating response: {str(e)}")


@app.post("/transcribe-audio", response_model=STTResponse)
async def transcribe_audio_endpoint(request: STTRequest):
    """Endpoint to transcribe audio from base64 encoded data"""
    try:
        transcript, confidence = await transcribe_audio(
            request.audio_base64,
            request.language_code or "en-US"
        )

        return STTResponse(
            transcript=transcript,
            confidence=confidence
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error transcribing audio: {str(e)}")


@app.post("/transcribe-audio-file")
async def transcribe_audio_file(file: UploadFile = File(...)):
    """Endpoint to transcribe audio from uploaded file"""
    try:
        # Read the uploaded file
        audio_content = await file.read()

        # Encode to base64
        audio_base64 = base64.b64encode(audio_content).decode('utf-8')

        # Transcribe
        transcript, confidence = await transcribe_audio(audio_base64)

        return {
            "transcript": transcript,
            "confidence": confidence,
            "filename": file.filename
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error transcribing audio file: {str(e)}")


@app.post("/generate-tts")
async def generate_tts_endpoint(request: TTSRequest):
    """Standalone endpoint to generate speech for any text"""
    try:
        audio_base64 = await generate_speech(
            request.text,
            request.voice_gender or "FEMALE",
            request.speaking_rate or 1.0
        )

        if audio_base64:
            return {"audio_base64": audio_base64, "success": True}
        else:
            raise HTTPException(
                status_code=500, detail="Failed to generate speech")

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating speech: {str(e)}")


@app.get("/tts-audio/{question_id}")
async def get_question_audio(question_id: int, voice_gender: str = "FEMALE", speaking_rate: float = 1.0):
    """Generate and return audio for a specific question's answer"""
    try:
        # Find the question
        question = next(
            (q for q in questions_log if q["id"] == question_id), None)
        if not question:
            raise HTTPException(status_code=404, detail="Question not found")

        # Generate audio
        audio_base64 = await generate_speech(
            question["answer"],
            voice_gender,
            speaking_rate
        )

        if not audio_base64:
            raise HTTPException(
                status_code=500, detail="Failed to generate audio")

        # Convert base64 to bytes
        audio_bytes = base64.b64decode(audio_base64)

        # Return as streaming response
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type="audio/mpeg",
            headers={
                "Content-Disposition": f"inline; filename=question_{question_id}_audio.mp3"
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating audio: {str(e)}")


@app.post("/score-all")
async def score_all_questions(background_tasks: BackgroundTasks):
    """Endpoint to trigger batch scoring of all unscored questions"""
    if not questions_log:
        raise HTTPException(status_code=400, detail="No questions to score")

    unscored_count = len([q for q in questions_log if q.get("score") is None])
    if unscored_count == 0:
        return {"message": "All questions are already scored", "total_questions": len(questions_log)}

    background_tasks.add_task(score_all_questions_async)

    return {
        "message": f"Batch scoring initiated for {unscored_count} questions",
        "total_questions": len(questions_log),
        "unscored_questions": unscored_count
    }


@app.post("/score-diagnosis", response_model=DiagnosisResponse)
async def score_diagnosis(request: DiagnosisRequest):
    """Endpoint to score a final diagnosis and reasoning"""
    if not context:
        raise HTTPException(
            status_code=400, detail="Context must be set before scoring diagnosis")

    if not questions_log:
        raise HTTPException(
            status_code=400, detail="No questions asked yet. Please ask diagnostic questions first")

    try:
        # Build the complete conversation history
        conversation_history = ""
        for q in questions_log:
            conversation_history += f"Q: {q['question']}\nA: {q['answer']}\n\n"

        diagnosis_scoring_prompt = f"""
        Patient/Case Context: {context}

        Conversation History:
        {conversation_history}

        Proposed Diagnosis: {request.diagnosis}
        Diagnostic Reasoning: {request.reasoning}

        Please evaluate this diagnostic reasoning and final diagnosis. Provide:
        1. A score from 0-100 based on:
           - Accuracy of diagnosis (0-40 points)
           - Quality of reasoning process (0-25 points)
           - Use of gathered information (0-20 points)
           - Clinical thinking and logic (0-15 points)

        2. Specific feedback on the diagnosis and reasoning
        3. Areas for improvement
        4. Strengths demonstrated

        Respond in the following JSON format:
        {{
            "diagnosis_score": [score_0_to_100],
            "feedback": "[detailed feedback about the diagnosis and reasoning]",
            "areas_for_improvement": ["area1", "area2", "area3"],
            "strengths": ["strength1", "strength2", "strength3"]
        }}

        Guidelines:
        - Be constructive and educational in feedback
        - Consider the information available from the questions asked
        - Evaluate both the diagnostic accuracy and the reasoning process
        - Provide specific, actionable suggestions for improvement
        - Acknowledge good clinical reasoning even if the diagnosis isn't perfect
        """

        response = genai_client.models.generate_content(
            model="gemini-2.5-flash",
            contents=diagnosis_scoring_prompt,
            config=types.GenerateContentConfig(
                temperature=0.2
            ),
        )

        # Parse the JSON response
        response_text = response.text.strip()

        # Clean up the response to extract JSON
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            result = json.loads(json_str)
        else:
            # Fallback parsing
            result = json.loads(response_text)

        # Validate and clean the response
        diagnosis_score = max(
            0, min(100, float(result.get("diagnosis_score", 0))))
        feedback = result.get("feedback", "No feedback provided")
        areas_for_improvement = result.get("areas_for_improvement", [])
        strengths = result.get("strengths", [])

        # Ensure lists are not empty
        if not areas_for_improvement:
            areas_for_improvement = [
                "Continue practicing systematic diagnostic reasoning"]
        if not strengths:
            strengths = ["Engaged in diagnostic process"]

        return DiagnosisResponse(
            diagnosis_score=round(diagnosis_score, 1),
            feedback=feedback,
            # Limit to 5 items
            areas_for_improvement=areas_for_improvement[:5],
            strengths=strengths[:5]  # Limit to 5 items
        )

    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing error in diagnosis scoring: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Error parsing diagnosis evaluation response")
    except Exception as e:
        logger.error(f"Error scoring diagnosis: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error evaluating diagnosis: {str(e)}")


@app.get("/questions", response_model=QuestionsListResponse)
async def get_questions():
    return QuestionsListResponse(
        questions=questions_log,
        total_count=len(questions_log)
    )


@app.delete("/questions")
async def clear_questions():
    global questions_log
    questions_log = []
    return {"message": "All questions cleared"}


@app.get("/score")
async def get_current_scores():
    if not questions_log:
        raise HTTPException(status_code=400, detail="No questions to score")

    scored_questions = [q for q in questions_log if q.get("score") is not None]
    pending_questions = [q for q in questions_log if q.get("score") is None]

    if not scored_questions:
        return {
            "total_questions": len(questions_log),
            "scored_questions": 0,
            "pending_questions": len(pending_questions),
            "average_score": 0,
            "individual_scores": [],
            "status": "scoring_pending"
        }

    scores = [q["score"] for q in scored_questions]
    average_score = sum(scores) / len(scores)

    individual_scores = [
        {
            "question_id": q["id"],
            "question": q["question"],
            "score": q.get("score"),
            "scoring_status": "completed" if q.get("score") is not None else "pending",
            "scoring_type": q.get("scoring_type", "unknown"),
            "answer": q["answer"][:100] + "..." if len(q["answer"]) > 100 else q["answer"],
            "scored_at": q.get("scored_at"),
            "has_audio": q.get("has_audio", False)
        }
        for q in questions_log
    ]

    return {
        "total_questions": len(questions_log),
        "scored_questions": len(scored_questions),
        "pending_questions": len(pending_questions),
        "average_score": round(average_score, 2),
        "individual_scores": individual_scores,
        "status": "partial" if pending_questions else "complete"
    }


@app.post("/manual-score")
async def manual_score_questions(request: ScoreRequest):
    if not questions_log:
        raise HTTPException(status_code=400, detail="No questions to score")

    if not request.question_scores:
        raise HTTPException(
            status_code=400, detail="Question scores are required")

    if len(request.question_scores) != len(questions_log):
        raise HTTPException(
            status_code=400,
            detail=f"Number of scores ({len(request.question_scores)}) must match number of questions ({len(questions_log)})"
        )

    try:
        for i, score in enumerate(request.question_scores):
            if not (0 <= score <= 10):
                raise HTTPException(
                    status_code=400, detail="Scores must be between 0 and 10")
            questions_log[i]["score"] = score
            questions_log[i]["scored_at"] = datetime.now().isoformat()
            questions_log[i]["scoring_type"] = "manual"

        average_score = sum(request.question_scores) / \
            len(request.question_scores)

        individual_scores = [
            {
                "question_id": q["id"],
                "question": q["question"],
                "score": q["score"],
                "answer": q["answer"][:100] + "..." if len(q["answer"]) > 100 else q["answer"],
                "has_audio": q.get("has_audio", False)
            }
            for q in questions_log
        ]

        return ScoreResponse(
            total_questions=len(questions_log),
            average_score=round(average_score, 2),
            individual_scores=individual_scores
        )

    except ValueError:
        raise HTTPException(
            status_code=400, detail="Invalid score format. Scores must be integers between 0 and 10")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error scoring questions: {str(e)}")


@app.get("/stats")
async def get_session_stats():
    scored_questions = [q for q in questions_log if q.get("score") is not None]
    pending_questions = [q for q in questions_log if q.get("score") is None]

    # Count scoring types
    scoring_types = {}
    for q in scored_questions:
        stype = q.get("scoring_type", "unknown")
        scoring_types[stype] = scoring_types.get(stype, 0) + 1

    return {
        "total_questions": len(questions_log),
        "scored_questions": len(scored_questions),
        "pending_questions": len(pending_questions),
        "average_score": round(sum(q["score"] for q in scored_questions) / len(scored_questions), 2) if scored_questions else 0,
        "context_set": bool(context),
        "session_active": len(questions_log) > 0,
        "scoring_status": "complete" if not pending_questions else "pending",
        "scoring_types": scoring_types,
        "stt_enabled": True  # Indicate that STT is available in this version
    }


if __name__ == "__main__":
    import uvicorn
    # Different port for STT version
    uvicorn.run(app, host="0.0.0.0", port=8000)
